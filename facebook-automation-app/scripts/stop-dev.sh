#!/bin/bash

# Facebook Automation App - Stop Development Services Script
# This script stops all running development services

set -e

echo "🛑 Stopping Facebook Automation App - Development Services"
echo "========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Stop service by PID file
stop_service() {
    local service_name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            print_status "Stopping $service_name (PID: $pid)..."
            kill "$pid"
            
            # Wait for process to stop
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                print_warning "Force killing $service_name..."
                kill -9 "$pid" 2>/dev/null || true
            fi
            
            print_status "$service_name stopped ✓"
        else
            print_warning "$service_name was not running"
        fi
        
        rm "$pid_file"
    else
        print_warning "No PID file found for $service_name"
    fi
}

# Stop services by port
stop_service_by_port() {
    local service_name=$1
    local port=$2
    
    print_status "Checking for $service_name on port $port..."
    
    # Find process using the port
    local pid=$(lsof -ti:$port 2>/dev/null || true)
    
    if [ -n "$pid" ]; then
        print_status "Stopping $service_name (PID: $pid) on port $port..."
        kill "$pid" 2>/dev/null || true
        
        # Wait for process to stop
        local count=0
        while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
            sleep 1
            count=$((count + 1))
        done
        
        # Force kill if still running
        if kill -0 "$pid" 2>/dev/null; then
            print_warning "Force killing $service_name..."
            kill -9 "$pid" 2>/dev/null || true
        fi
        
        print_status "$service_name stopped ✓"
    else
        print_warning "No process found on port $port"
    fi
}

# Main stop function
stop_all_services() {
    print_step "Stopping all development services..."
    
    # Change to project root directory
    cd "$(dirname "$0")/.."
    
    # Stop services using PID files
    if [ -d "logs" ]; then
        stop_service "React Frontend" "logs/frontend.pid"
        stop_service "Python Backend" "logs/backend.pid"
        stop_service "CollyParser Service" "logs/colly-parser.pid"
        stop_service "ChromedpExtractor Service" "logs/chromedp-extractor.pid"
    else
        print_warning "Logs directory not found, trying to stop by port..."
    fi
    
    # Fallback: stop services by port
    print_step "Checking for remaining services on ports..."
    stop_service_by_port "React Frontend" "3000"
    stop_service_by_port "Python Backend" "8000"
    stop_service_by_port "CollyParser Service" "8082"
    stop_service_by_port "ChromedpExtractor Service" "8081"
    
    # Stop any remaining Node.js processes related to the project
    print_step "Cleaning up remaining processes..."
    
    # Kill any remaining npm/node processes for this project
    pkill -f "npm start" 2>/dev/null || true
    pkill -f "react-scripts start" 2>/dev/null || true
    
    # Kill any remaining Python processes for this project
    pkill -f "python main.py" 2>/dev/null || true
    pkill -f "uvicorn" 2>/dev/null || true
    
    # Kill any remaining Go service processes
    pkill -f "chromedp-extractor" 2>/dev/null || true
    pkill -f "colly-parser" 2>/dev/null || true
    
    print_status "All services stopped successfully ✓"
}

# Clean up log files
cleanup_logs() {
    print_step "Cleaning up log files..."
    
    if [ -d "logs" ]; then
        # Remove PID files
        rm -f logs/*.pid
        
        # Optionally remove log files (uncomment if desired)
        # rm -f logs/*.log
        
        print_status "Log cleanup completed ✓"
    fi
}

# Display final status
show_final_status() {
    echo ""
    echo "✅ All Facebook Automation App services have been stopped"
    echo "========================================================"
    echo ""
    echo "📊 Port Status:"
    
    # Check if ports are free
    for port in 3000 8000 8081 8082; do
        if lsof -ti:$port >/dev/null 2>&1; then
            echo "  • Port $port: ❌ Still in use"
        else
            echo "  • Port $port: ✅ Free"
        fi
    done
    
    echo ""
    echo "🚀 To start services again, run: ./scripts/start-dev.sh"
    echo "🧹 To clean all build artifacts, run: ./scripts/clean.sh"
    echo ""
}

# Force stop function for stubborn processes
force_stop() {
    print_warning "Force stopping all related processes..."
    
    # Force kill by process name
    pkill -9 -f "chromedp-extractor" 2>/dev/null || true
    pkill -9 -f "colly-parser" 2>/dev/null || true
    pkill -9 -f "python main.py" 2>/dev/null || true
    pkill -9 -f "npm start" 2>/dev/null || true
    pkill -9 -f "react-scripts" 2>/dev/null || true
    
    # Force kill by port
    for port in 3000 8000 8081 8082; do
        local pid=$(lsof -ti:$port 2>/dev/null || true)
        if [ -n "$pid" ]; then
            kill -9 "$pid" 2>/dev/null || true
        fi
    done
    
    print_status "Force stop completed ✓"
}

# Main execution
main() {
    # Parse command line arguments
    if [ "$1" = "--force" ] || [ "$1" = "-f" ]; then
        force_stop
    else
        stop_all_services
    fi
    
    cleanup_logs
    show_final_status
}

# Handle script arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Facebook Automation App - Stop Development Services"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --force, -f    Force stop all processes"
    echo "  --help, -h     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Normal stop"
    echo "  $0 --force      # Force stop all processes"
    echo ""
    exit 0
fi

# Run main function
main "$@"
