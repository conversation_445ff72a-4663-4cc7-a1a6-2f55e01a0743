#!/bin/bash

# Facebook Automation App - Comprehensive Test Suite
# This script runs all tests for the hybrid system

set -e

echo "🧪 Running Facebook Automation App Test Suite"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Test configuration
TEST_RESULTS_DIR="test-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Initialize test environment
init_test_env() {
    print_step "Initializing test environment..."
    
    # Create test results directory
    mkdir -p "$TEST_RESULTS_DIR"
    
    # Change to project root
    cd "$(dirname "$0")/.."
    
    print_status "Test environment initialized ✓"
}

# Check test prerequisites
check_test_prerequisites() {
    print_step "Checking test prerequisites..."
    
    # Check if services are running
    services_running=true
    
    if ! curl -s http://localhost:8081/health > /dev/null; then
        print_warning "ChromedpExtractor service not running"
        services_running=false
    fi
    
    if ! curl -s http://localhost:8082/health > /dev/null; then
        print_warning "CollyParser service not running"
        services_running=false
    fi
    
    if ! curl -s http://localhost:8000/health > /dev/null; then
        print_warning "Python backend not running"
        services_running=false
    fi
    
    if [ "$services_running" = false ]; then
        print_warning "Some services are not running. Starting services..."
        ./scripts/start-dev.sh &
        START_SCRIPT_PID=$!
        
        # Wait for services to start
        print_status "Waiting for services to start..."
        sleep 30
        
        # Kill the start script (it runs indefinitely)
        kill $START_SCRIPT_PID 2>/dev/null || true
    fi
    
    print_status "Test prerequisites checked ✓"
}

# Run Go services tests
test_go_services() {
    print_step "Testing Go microservices..."
    
    # Test ChromedpExtractor
    print_status "Testing ChromedpExtractor service..."
    cd backend/go-services/chromedp-extractor
    go test -v ./... > "../../../$TEST_RESULTS_DIR/chromedp-extractor-tests_$TIMESTAMP.log" 2>&1
    chromedp_result=$?
    cd ../../..
    
    # Test CollyParser
    print_status "Testing CollyParser service..."
    cd backend/go-services/colly-parser
    go test -v ./... > "../../../$TEST_RESULTS_DIR/colly-parser-tests_$TIMESTAMP.log" 2>&1
    colly_result=$?
    cd ../../..
    
    if [ $chromedp_result -eq 0 ] && [ $colly_result -eq 0 ]; then
        print_status "Go services tests passed ✓"
        return 0
    else
        print_error "Go services tests failed ✗"
        return 1
    fi
}

# Run Python backend tests
test_python_backend() {
    print_step "Testing Python backend..."
    
    cd backend
    
    # Activate virtual environment
    if [ -d "venv" ]; then
        source venv/bin/activate
    fi
    
    # Run pytest
    print_status "Running pytest..."
    pytest tests/ -v --tb=short > "../$TEST_RESULTS_DIR/backend-tests_$TIMESTAMP.log" 2>&1
    pytest_result=$?
    
    cd ..
    
    if [ $pytest_result -eq 0 ]; then
        print_status "Python backend tests passed ✓"
        return 0
    else
        print_error "Python backend tests failed ✗"
        return 1
    fi
}

# Run hybrid system tests
test_hybrid_system() {
    print_step "Testing hybrid system integration..."
    
    cd backend
    
    # Activate virtual environment
    if [ -d "venv" ]; then
        source venv/bin/activate
    fi
    
    # Run hybrid system tests
    print_status "Running hybrid integration tests..."
    python -m automation.integration_testing > "../$TEST_RESULTS_DIR/integration-tests_$TIMESTAMP.log" 2>&1
    integration_result=$?
    
    print_status "Running communication tests..."
    python -m automation.communication_test > "../$TEST_RESULTS_DIR/communication-tests_$TIMESTAMP.log" 2>&1
    communication_result=$?
    
    print_status "Running error handling tests..."
    python -m automation.recovery_testing > "../$TEST_RESULTS_DIR/recovery-tests_$TIMESTAMP.log" 2>&1
    recovery_result=$?
    
    cd ..
    
    if [ $integration_result -eq 0 ] && [ $communication_result -eq 0 ] && [ $recovery_result -eq 0 ]; then
        print_status "Hybrid system tests passed ✓"
        return 0
    else
        print_error "Hybrid system tests failed ✗"
        return 1
    fi
}

# Run performance tests
test_performance() {
    print_step "Running performance tests..."
    
    cd backend
    
    # Activate virtual environment
    if [ -d "venv" ]; then
        source venv/bin/activate
    fi
    
    # Run performance benchmarks
    print_status "Running performance benchmarks..."
    python -m automation.performance_testing > "../$TEST_RESULTS_DIR/performance-tests_$TIMESTAMP.log" 2>&1
    performance_result=$?
    
    print_status "Running memory profiling..."
    python -m automation.memory_profiler > "../$TEST_RESULTS_DIR/memory-profiling_$TIMESTAMP.log" 2>&1
    memory_result=$?
    
    cd ..
    
    if [ $performance_result -eq 0 ] && [ $memory_result -eq 0 ]; then
        print_status "Performance tests passed ✓"
        return 0
    else
        print_warning "Performance tests completed with warnings ⚠️"
        return 0  # Performance tests are informational
    fi
}

# Run comprehensive test suite
test_comprehensive() {
    print_step "Running comprehensive test suite..."
    
    cd backend
    
    # Activate virtual environment
    if [ -d "venv" ]; then
        source venv/bin/activate
    fi
    
    # Run comprehensive performance suite
    print_status "Running comprehensive performance suite..."
    python -m automation.comprehensive_performance_suite > "../$TEST_RESULTS_DIR/comprehensive-performance_$TIMESTAMP.log" 2>&1
    perf_suite_result=$?
    
    # Run comprehensive integration suite
    print_status "Running comprehensive integration suite..."
    python -m automation.comprehensive_integration_suite > "../$TEST_RESULTS_DIR/comprehensive-integration_$TIMESTAMP.log" 2>&1
    integration_suite_result=$?
    
    cd ..
    
    if [ $perf_suite_result -eq 0 ] && [ $integration_suite_result -eq 0 ]; then
        print_status "Comprehensive test suite passed ✓"
        return 0
    else
        print_error "Comprehensive test suite failed ✗"
        return 1
    fi
}

# Run frontend tests
test_frontend() {
    print_step "Testing React frontend..."
    
    cd frontend
    
    # Run npm tests
    print_status "Running frontend unit tests..."
    npm test -- --coverage --watchAll=false > "../$TEST_RESULTS_DIR/frontend-tests_$TIMESTAMP.log" 2>&1
    frontend_result=$?
    
    cd ..
    
    if [ $frontend_result -eq 0 ]; then
        print_status "Frontend tests passed ✓"
        return 0
    else
        print_error "Frontend tests failed ✗"
        return 1
    fi
}

# Run load tests
test_load() {
    print_step "Running load tests..."
    
    cd backend
    
    # Activate virtual environment
    if [ -d "venv" ]; then
        source venv/bin/activate
    fi
    
    # Run load tests
    print_status "Running load testing suite..."
    python -m automation.load_testing > "../$TEST_RESULTS_DIR/load-tests_$TIMESTAMP.log" 2>&1
    load_result=$?
    
    cd ..
    
    if [ $load_result -eq 0 ]; then
        print_status "Load tests passed ✓"
        return 0
    else
        print_warning "Load tests completed with warnings ⚠️"
        return 0  # Load tests are informational
    fi
}

# Generate test report
generate_test_report() {
    print_step "Generating test report..."
    
    report_file="$TEST_RESULTS_DIR/test-report_$TIMESTAMP.txt"
    
    cat > "$report_file" << EOF
Facebook Automation App - Test Suite Report
==========================================

Test Date: $(date)
Test Platform: $(uname -s) $(uname -m)

Test Categories:
- Go Microservices Tests
- Python Backend Tests
- Hybrid System Integration Tests
- Performance Tests
- Frontend Tests
- Load Tests
- Comprehensive Test Suites

Test Results Summary:
$(if [ ${test_results[go_services]} -eq 0 ]; then echo "✅ Go Services: PASSED"; else echo "❌ Go Services: FAILED"; fi)
$(if [ ${test_results[python_backend]} -eq 0 ]; then echo "✅ Python Backend: PASSED"; else echo "❌ Python Backend: FAILED"; fi)
$(if [ ${test_results[hybrid_system]} -eq 0 ]; then echo "✅ Hybrid System: PASSED"; else echo "❌ Hybrid System: FAILED"; fi)
$(if [ ${test_results[performance]} -eq 0 ]; then echo "✅ Performance: PASSED"; else echo "⚠️ Performance: WARNING"; fi)
$(if [ ${test_results[frontend]} -eq 0 ]; then echo "✅ Frontend: PASSED"; else echo "❌ Frontend: FAILED"; fi)
$(if [ ${test_results[load]} -eq 0 ]; then echo "✅ Load Tests: PASSED"; else echo "⚠️ Load Tests: WARNING"; fi)
$(if [ ${test_results[comprehensive]} -eq 0 ]; then echo "✅ Comprehensive: PASSED"; else echo "❌ Comprehensive: FAILED"; fi)

Overall Status: $(if [ $overall_result -eq 0 ]; then echo "✅ ALL TESTS PASSED"; else echo "❌ SOME TESTS FAILED"; fi)

Detailed logs available in: $TEST_RESULTS_DIR/

Recommendations:
- Review failed test logs for specific issues
- Run individual test categories for debugging
- Check service connectivity and configuration
- Verify system resources and performance

Test completed at: $(date)
EOF
    
    print_status "Test report generated: $report_file ✓"
}

# Display test summary
show_test_summary() {
    echo ""
    echo "🎯 Test Suite Execution Summary"
    echo "==============================="
    echo ""
    
    # Display results
    echo "📊 Test Results:"
    if [ ${test_results[go_services]} -eq 0 ]; then
        echo "  ✅ Go Services Tests: PASSED"
    else
        echo "  ❌ Go Services Tests: FAILED"
    fi
    
    if [ ${test_results[python_backend]} -eq 0 ]; then
        echo "  ✅ Python Backend Tests: PASSED"
    else
        echo "  ❌ Python Backend Tests: FAILED"
    fi
    
    if [ ${test_results[hybrid_system]} -eq 0 ]; then
        echo "  ✅ Hybrid System Tests: PASSED"
    else
        echo "  ❌ Hybrid System Tests: FAILED"
    fi
    
    if [ ${test_results[performance]} -eq 0 ]; then
        echo "  ✅ Performance Tests: PASSED"
    else
        echo "  ⚠️ Performance Tests: WARNING"
    fi
    
    if [ ${test_results[frontend]} -eq 0 ]; then
        echo "  ✅ Frontend Tests: PASSED"
    else
        echo "  ❌ Frontend Tests: FAILED"
    fi
    
    if [ ${test_results[load]} -eq 0 ]; then
        echo "  ✅ Load Tests: PASSED"
    else
        echo "  ⚠️ Load Tests: WARNING"
    fi
    
    if [ ${test_results[comprehensive]} -eq 0 ]; then
        echo "  ✅ Comprehensive Tests: PASSED"
    else
        echo "  ❌ Comprehensive Tests: FAILED"
    fi
    
    echo ""
    echo "📁 Test Results: $TEST_RESULTS_DIR/"
    echo "📋 Test Report: $TEST_RESULTS_DIR/test-report_$TIMESTAMP.txt"
    echo ""
    
    if [ $overall_result -eq 0 ]; then
        echo "🎉 All tests passed! System is ready for production."
    else
        echo "⚠️ Some tests failed. Please review the logs and fix issues."
    fi
    
    echo ""
}

# Main execution
main() {
    # Parse command line arguments
    RUN_ALL=true
    RUN_GO=false
    RUN_PYTHON=false
    RUN_HYBRID=false
    RUN_PERFORMANCE=false
    RUN_FRONTEND=false
    RUN_LOAD=false
    RUN_COMPREHENSIVE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --go)
                RUN_ALL=false
                RUN_GO=true
                shift
                ;;
            --python)
                RUN_ALL=false
                RUN_PYTHON=true
                shift
                ;;
            --hybrid)
                RUN_ALL=false
                RUN_HYBRID=true
                shift
                ;;
            --performance)
                RUN_ALL=false
                RUN_PERFORMANCE=true
                shift
                ;;
            --frontend)
                RUN_ALL=false
                RUN_FRONTEND=true
                shift
                ;;
            --load)
                RUN_ALL=false
                RUN_LOAD=true
                shift
                ;;
            --comprehensive)
                RUN_ALL=false
                RUN_COMPREHENSIVE=true
                shift
                ;;
            --help|-h)
                echo "Facebook Automation App - Test Suite"
                echo ""
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --go              Run Go services tests only"
                echo "  --python          Run Python backend tests only"
                echo "  --hybrid          Run hybrid system tests only"
                echo "  --performance     Run performance tests only"
                echo "  --frontend        Run frontend tests only"
                echo "  --load            Run load tests only"
                echo "  --comprehensive   Run comprehensive test suites only"
                echo "  --help, -h        Show this help message"
                echo ""
                echo "Examples:"
                echo "  $0                # Run all tests"
                echo "  $0 --hybrid       # Run only hybrid system tests"
                echo "  $0 --performance  # Run only performance tests"
                echo ""
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Initialize
    init_test_env
    check_test_prerequisites
    
    # Initialize test results array
    declare -A test_results
    overall_result=0
    
    # Run selected tests
    if [ "$RUN_ALL" = true ] || [ "$RUN_GO" = true ]; then
        test_go_services
        test_results[go_services]=$?
        if [ ${test_results[go_services]} -ne 0 ]; then overall_result=1; fi
    fi
    
    if [ "$RUN_ALL" = true ] || [ "$RUN_PYTHON" = true ]; then
        test_python_backend
        test_results[python_backend]=$?
        if [ ${test_results[python_backend]} -ne 0 ]; then overall_result=1; fi
    fi
    
    if [ "$RUN_ALL" = true ] || [ "$RUN_HYBRID" = true ]; then
        test_hybrid_system
        test_results[hybrid_system]=$?
        if [ ${test_results[hybrid_system]} -ne 0 ]; then overall_result=1; fi
    fi
    
    if [ "$RUN_ALL" = true ] || [ "$RUN_PERFORMANCE" = true ]; then
        test_performance
        test_results[performance]=$?
        # Performance tests don't affect overall result
    fi
    
    if [ "$RUN_ALL" = true ] || [ "$RUN_FRONTEND" = true ]; then
        test_frontend
        test_results[frontend]=$?
        if [ ${test_results[frontend]} -ne 0 ]; then overall_result=1; fi
    fi
    
    if [ "$RUN_ALL" = true ] || [ "$RUN_LOAD" = true ]; then
        test_load
        test_results[load]=$?
        # Load tests don't affect overall result
    fi
    
    if [ "$RUN_ALL" = true ] || [ "$RUN_COMPREHENSIVE" = true ]; then
        test_comprehensive
        test_results[comprehensive]=$?
        if [ ${test_results[comprehensive]} -ne 0 ]; then overall_result=1; fi
    fi
    
    # Generate report and summary
    generate_test_report
    show_test_summary
    
    exit $overall_result
}

# Run main function
main "$@"
