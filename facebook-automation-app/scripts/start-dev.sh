#!/bin/bash

# Facebook Automation App - Development Startup Script
# This script starts all services required for development

set -e

echo "🚀 Starting Facebook Automation App - Development Mode"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo "${BLUE}[STEP]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_step "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 16.x or higher."
        exit 1
    fi
    
    # Check Python
    if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
        print_error "Python is not installed. Please install Python 3.8 or higher."
        exit 1
    fi
    
    # Check Go
    if ! command -v go &> /dev/null; then
        print_error "Go is not installed. Please install Go 1.19 or higher."
        exit 1
    fi
    
    print_status "All prerequisites are installed ✓"
}

# Build Go services
build_go_services() {
    print_step "Building Go microservices..."

    # Create Go services directories if they don't exist
    mkdir -p backend/go-services/chromedp-extractor
    mkdir -p backend/go-services/colly-parser

    # Build ChromedpExtractor
    print_status "Building ChromedpExtractor service..."
    cd backend/go-services/chromedp-extractor

    # Check if service exists
    if [ ! -f "main.go" ]; then
        print_error "ChromedpExtractor main.go not found. Please ensure Go services are properly set up."
        exit 1
    fi


    go mod tidy
    go build -o chromedp-extractor main.go
    chmod +x chromedp-extractor
    cd ../../..

    # Build CollyParser
    print_status "Building CollyParser service..."
    cd backend/go-services/colly-parser

    # Check if service exists
    if [ ! -f "main.go" ]; then
        print_error "CollyParser main.go not found. Please ensure Go services are properly set up."
        exit 1
    fi


    go mod tidy
    go build -o colly-parser main.go
    chmod +x colly-parser
    cd ../../..

    print_status "Go services built successfully ✓"
}

# Setup Python backend
setup_backend() {
    print_step "Setting up Python backend..."
    
    cd backend
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    print_status "Activating virtual environment..."
    source venv/bin/activate
    
    # Install dependencies
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Check if .env exists
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            print_status "Creating .env from .env.example..."
            cp .env.example .env
        else
            print_warning ".env file not found. Please create one manually."
        fi
    fi
    
    cd ..
    print_status "Backend setup completed ✓"
}

# Setup frontend
setup_frontend() {
    print_step "Setting up React frontend..."
    
    cd frontend
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        print_status "Installing Node.js dependencies..."
        npm install
    fi
    
    cd ..
    print_status "Frontend setup completed ✓"
}

# Start services
start_services() {
    print_step "Starting all services..."
    
    # Create logs directory
    mkdir -p logs
    
    # Start ChromedpExtractor service
    print_status "Starting ChromedpExtractor service on port 8081..."
    cd backend/go-services/chromedp-extractor
    ./chromedp-extractor > ../../../logs/chromedp-extractor.log 2>&1 &
    CHROMEDP_PID=$!
    echo $CHROMEDP_PID > ../../../logs/chromedp-extractor.pid
    cd ../../..
    
    # Wait a moment for service to start
    sleep 2
    
    # Start CollyParser service
    print_status "Starting CollyParser service on port 8082..."
    cd backend/go-services/colly-parser
    ./colly-parser > ../../../logs/colly-parser.log 2>&1 &
    COLLY_PID=$!
    echo $COLLY_PID > ../../../logs/colly-parser.pid
    cd ../../..
    
    # Wait a moment for service to start
    sleep 2
    
    # Start Python backend
    print_status "Starting Python backend on port 8000..."
    cd backend
    source venv/bin/activate
    python main.py > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../logs/backend.pid
    cd ..
    
    # Wait a moment for backend to start
    sleep 3
    
    # Start frontend
    print_status "Starting React frontend on port 3000..."
    cd frontend
    npm start > ../logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../logs/frontend.pid
    cd ..
    
    print_status "All services started successfully ✓"
}

# Check service health
check_services() {
    print_step "Checking service health..."
    
    # Wait for services to fully start
    sleep 5
    
    # Check ChromedpExtractor
    if curl -s http://localhost:8081/health > /dev/null; then
        print_status "ChromedpExtractor service is healthy ✓"
    else
        print_warning "ChromedpExtractor service may not be ready yet"
    fi
    
    # Check CollyParser
    if curl -s http://localhost:8082/health > /dev/null; then
        print_status "CollyParser service is healthy ✓"
    else
        print_warning "CollyParser service may not be ready yet"
    fi
    
    # Check Python backend
    if curl -s http://localhost:8000/health > /dev/null; then
        print_status "Python backend is healthy ✓"
    else
        print_warning "Python backend may not be ready yet"
    fi
    
    # Check frontend (it takes longer to start)
    print_status "Frontend is starting... (this may take a moment)"
}

# Display service information
show_service_info() {
    echo ""
    echo "🎉 Facebook Automation App is now running!"
    echo "=========================================="
    echo ""
    echo "📊 Service Status:"
    echo "  • ChromedpExtractor: http://localhost:8081"
    echo "  • CollyParser:       http://localhost:8082"
    echo "  • Python Backend:    http://localhost:8000"
    echo "  • React Frontend:    http://localhost:3000"
    echo ""
    echo "📁 Logs are available in the 'logs/' directory"
    echo "📋 Process IDs are stored in 'logs/*.pid' files"
    echo ""
    echo "🛑 To stop all services, run: ./scripts/stop-dev.sh"
    echo "🧪 To run tests, run: ./scripts/test-suite.sh"
    echo ""
    echo "🌐 Open your browser and navigate to: http://localhost:3000"
    echo ""
}

# Cleanup function
cleanup() {
    print_warning "Received interrupt signal. Cleaning up..."
    
    # Kill services if PIDs exist
    if [ -f "logs/frontend.pid" ]; then
        kill $(cat logs/frontend.pid) 2>/dev/null || true
        rm logs/frontend.pid
    fi
    
    if [ -f "logs/backend.pid" ]; then
        kill $(cat logs/backend.pid) 2>/dev/null || true
        rm logs/backend.pid
    fi
    
    if [ -f "logs/colly-parser.pid" ]; then
        kill $(cat logs/colly-parser.pid) 2>/dev/null || true
        rm logs/colly-parser.pid
    fi
    
    if [ -f "logs/chromedp-extractor.pid" ]; then
        kill $(cat logs/chromedp-extractor.pid) 2>/dev/null || true
        rm logs/chromedp-extractor.pid
    fi
    
    print_status "Cleanup completed"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    # Change to project root directory
    cd "$(dirname "$0")/.."
    
    check_prerequisites
    build_go_services
    setup_backend
    setup_frontend
    start_services
    check_services
    show_service_info
    
    # Keep script running
    print_status "Press Ctrl+C to stop all services"
    while true; do
        sleep 1
    done
}

# Run main function
main "$@"
