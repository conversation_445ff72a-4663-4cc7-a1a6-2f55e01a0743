"""
Zendriver service for managing antidetect browser sessions.
"""
import asyncio
import json
import os
import subprocess
import sys
import time
from typing import Dict, Any, Optional, List
import logging

from ..models.profile import Profile
from .browser_service import BrowserService

logger = logging.getLogger(__name__)


class ZendriverService:
    """Service for managing zendriver browser sessions."""
    
    def __init__(self):
        self.browser_service = BrowserService()
        self.active_sessions = {}  # profile_id -> session_info
        self.session_timeout = 3600  # 1 hour
        
    async def launch_browser_for_facebook_login(self, profile: Profile) -> Dict[str, Any]:
        """Launch antidetect browser using zendriver for Facebook login."""
        try:
            # Check if session already exists
            if profile.id in self.active_sessions:
                session_info = self.active_sessions[profile.id]
                if self._is_session_active(session_info):
                    return {
                        "status": "session_exists",
                        "message": "Browser session already active for this profile",
                        "session_id": session_info["session_id"]
                    }
                else:
                    # Clean up expired session
                    await self._cleanup_session(profile.id)

            # Use direct zendriver launch only (more reliable)
            result = await self._launch_browser_directly(profile)
            return result

        except Exception as e:
            logger.error(f"Error launching browser for profile {profile.id}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to launch browser: {str(e)}"
            }

    async def _launch_browser_directly(self, profile: Profile) -> Dict[str, Any]:
        """Launch browser directly using zendriver without subprocess."""
        try:
            # Import zendriver with proper error handling
            import sys
            zendriver_path = os.path.join(os.path.dirname(__file__), "../../zendriver_local")
            if zendriver_path not in sys.path:
                sys.path.insert(0, zendriver_path)

            try:
                import zendriver as zd
                logger.info("✓ Zendriver imported successfully")
            except ImportError as import_error:
                logger.error(f"Failed to import zendriver: {import_error}")
                raise Exception(f"Zendriver not available: {import_error}")

            # Get zendriver configuration with validation
            try:
                zendriver_config = self.browser_service.get_zendriver_config(profile)
                logger.info(f"✓ Zendriver config generated for profile {profile.name}")
            except Exception as config_error:
                logger.error(f"Failed to generate zendriver config: {config_error}")
                raise Exception(f"Configuration error: {config_error}")

            # Ensure user_data_dir exists
            user_data_dir = zendriver_config.get("user_data_dir")
            if not user_data_dir:
                raise Exception("user_data_dir not specified in configuration")

            try:
                os.makedirs(user_data_dir, exist_ok=True)
                logger.info(f"✓ Profile directory created: {user_data_dir}")
            except Exception as dir_error:
                logger.error(f"Failed to create profile directory: {dir_error}")
                raise Exception(f"Directory creation failed: {dir_error}")

            # Validate Chrome executable
            chrome_path = zendriver_config.get("browser_executable_path")
            if chrome_path and not os.path.exists(chrome_path):
                logger.error(f"Chrome executable not found: {chrome_path}")
                raise Exception(f"Chrome not found at: {chrome_path}")

            logger.info(f"Starting browser directly for profile {profile.name}")
            logger.info(f"Chrome path: {chrome_path}")
            logger.info(f"Profile dir: {user_data_dir}")

            # Start browser with detailed error handling
            try:
                browser = await zd.start(**zendriver_config)
                logger.info("✓ Browser started successfully")
            except Exception as browser_error:
                logger.error(f"Browser start failed: {browser_error}")
                raise Exception(f"Browser launch failed: {browser_error}")

            # Navigate to Facebook login with error handling
            try:
                page = await browser.get("https://www.facebook.com/login")
                logger.info("✓ Navigated to Facebook login page")
            except Exception as nav_error:
                logger.error(f"Navigation failed: {nav_error}")
                try:
                    await browser.stop()
                except:
                    pass
                raise Exception(f"Navigation to Facebook failed: {nav_error}")

            # Set page title to identify the session
            try:
                await page.evaluate(f"""
                    document.title = 'Facebook Login - {profile.name} - Complete login manually then click Complete Login in app';
                """)
                logger.info("✓ Page title updated")
            except Exception as title_error:
                logger.warning(f"Failed to set page title: {title_error}")

            # Store session info
            session_info = {
                "session_id": f"fb_login_{profile.id}_{int(time.time())}",
                "profile_id": profile.id,
                "browser": browser,
                "page": page,
                "started_at": time.time(),
                "status": "active",
                "method": "direct"
            }

            self.active_sessions[profile.id] = session_info

            logger.info(f"✓ Browser launched directly for Facebook login - Profile: {profile.name} ({profile.id})")

            return {
                "status": "browser_launched",
                "message": "Antidetect browser launched successfully. Complete Facebook login manually.",
                "session_id": session_info["session_id"],
                "instructions": self._get_login_instructions()
            }

        except Exception as e:
            logger.error(f"Error in direct browser launch: {str(e)}")
            # Clean up any partial state
            if profile.id in self.active_sessions:
                try:
                    session_info = self.active_sessions[profile.id]
                    browser = session_info.get("browser")
                    if browser:
                        await browser.stop()
                except:
                    pass
                del self.active_sessions[profile.id]

            raise Exception(f"Failed to launch browser: {str(e)}")

    def _create_zendriver_script(self, script_path: str, zendriver_config: Dict, profile: Profile):
        """Create Python script for zendriver browser automation."""
        # Convert config to proper Python format
        config_str = json.dumps(zendriver_config, indent=8).replace('true', 'True').replace('false', 'False').replace('null', 'None')

        script_content = f'''
import asyncio
import sys
import os
import json
import time

# Add zendriver to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../zendriver_local"))

try:
    import zendriver as zd
except ImportError as e:
    print(f"Error importing zendriver: {{e}}")
    print("Make sure zendriver is installed and accessible")
    sys.exit(1)

async def main():
    """Launch antidetect browser for Facebook login."""
    try:
        print("Starting antidetect browser for Facebook login...")
        print("Profile: {profile.name} (ID: {profile.id})")

        # Browser configuration with antidetect settings
        config = {config_str}

        # Start browser with antidetect configuration
        browser = await zd.start(**config)
        print("Browser started successfully")

        # Navigate to Facebook login
        page = await browser.get("https://www.facebook.com/login")
        print("Navigated to Facebook login page")

        # Set page title to identify the session
        await page.evaluate("""
            document.title = 'Facebook Login - {profile.name} - Complete login manually then click Complete Login in app';
        """)

        print("\\n" + "="*60)
        print("FACEBOOK LOGIN INSTRUCTIONS:")
        print("1. Enter your Facebook email/phone and password")
        print("2. Complete any 2FA verification if prompted")
        print("3. Solve any CAPTCHA or security challenges")
        print("4. Once logged in, click 'Complete Login' in the app")
        print("="*60 + "\\n")

        # Keep browser open for manual login
        # Browser will stay open until user completes login or timeout
        session_file = os.path.join(os.path.dirname(__file__), "session_status.json")

        start_time = time.time()
        timeout = 3600  # 1 hour

        while time.time() - start_time < timeout:
            # Check if session should be terminated
            if os.path.exists(session_file):
                with open(session_file, 'r') as f:
                    status = json.load(f)
                    if status.get('action') == 'terminate':
                        print("Session termination requested")
                        break

            # Check if user is logged in by looking for Facebook elements
            try:
                # Check if we're on Facebook homepage or logged in
                current_url = page.url
                if 'facebook.com' in current_url and '/login' not in current_url:
                    print("Login detected! User appears to be logged in.")
                    # Update session status
                    with open(session_file, 'w') as f:
                        json.dump({{
                            'status': 'logged_in',
                            'url': current_url,
                            'timestamp': time.time()
                        }}, f)
            except Exception as e:
                print(f"Error checking login status: {{e}}")

            await asyncio.sleep(5)  # Check every 5 seconds

        print("Session ending...")
        await browser.stop()

    except Exception as e:
        print(f"Error in browser session: {{e}}")
        import traceback
        traceback.print_exc()

    print("Browser session ended")

if __name__ == "__main__":
    asyncio.run(main())
'''

        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
    
    async def _launch_browser_process(self, script_path: str, session_dir: str) -> subprocess.Popen:
        """Launch the browser process."""
        # Use Python to run the zendriver script
        cmd = [sys.executable, script_path]
        
        # Launch process with error handling
        try:
            process = subprocess.Popen(
                cmd,
                cwd=session_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Give it a moment to start
            await asyncio.sleep(2)

            # Check if process is still running
            if process.poll() is not None:
                # Process has already terminated
                stdout, stderr = process.communicate()
                logger.error(f"Browser process terminated immediately. stdout: {stdout}, stderr: {stderr}")
                raise Exception(f"Browser process failed to start: {stderr}")

            return process

        except Exception as e:
            logger.error(f"Failed to launch browser process: {str(e)}")
            raise Exception(f"Failed to launch browser: {str(e)}")
    
    def _get_session_directory(self, profile: Profile) -> str:
        """Get session directory for profile."""
        base_dir = os.path.join(os.getcwd(), "browser_sessions")
        return os.path.join(base_dir, f"profile_{profile.id}")
    
    def _is_session_active(self, session_info: Dict) -> bool:
        """Check if browser session is still active."""
        if not session_info:
            return False

        # Check timeout first
        elapsed = time.time() - session_info.get("started_at", 0)
        if elapsed >= self.session_timeout:
            return False

        # Check if it's a direct browser session
        if session_info.get("method") == "direct":
            browser = session_info.get("browser")
            if browser:
                try:
                    # Try to check if browser is still connected
                    # This is a simple check - in practice you might want more sophisticated checking
                    return True  # Assume active if browser object exists
                except Exception:
                    return False
            return False

        # Check if process is still running (subprocess method)
        process = session_info.get("process")
        if process and process.poll() is None:
            return True

        return False
    
    async def _cleanup_session(self, profile_id: str):
        """Clean up browser session."""
        if profile_id in self.active_sessions:
            session_info = self.active_sessions[profile_id]

            # Handle direct browser session
            if session_info.get("method") == "direct":
                browser = session_info.get("browser")
                if browser:
                    try:
                        await browser.stop()
                        logger.info(f"Direct browser session stopped for profile {profile_id}")
                    except Exception as e:
                        logger.error(f"Error stopping direct browser session: {e}")

            # Handle subprocess session
            process = session_info.get("process")
            if process and process.poll() is None:
                try:
                    process.terminate()
                    await asyncio.sleep(1)
                    if process.poll() is None:
                        process.kill()
                except Exception as e:
                    logger.error(f"Error terminating process: {e}")

            # Remove from active sessions
            del self.active_sessions[profile_id]
            logger.info(f"Cleaned up session for profile {profile_id}")
    
    async def terminate_session(self, profile_id: str) -> Dict[str, Any]:
        """Terminate browser session for profile."""
        try:
            if profile_id not in self.active_sessions:
                return {
                    "status": "no_session",
                    "message": "No active session found for this profile"
                }

            session_info = self.active_sessions[profile_id]

            # Signal session to terminate (only for subprocess sessions)
            if session_info.get("method") != "direct" and session_info.get("session_dir"):
                try:
                    session_file = os.path.join(session_info["session_dir"], "session_status.json")
                    with open(session_file, 'w') as f:
                        json.dump({"action": "terminate", "timestamp": time.time()}, f)
                except Exception as signal_error:
                    logger.warning(f"Failed to signal session termination: {signal_error}")

            # Clean up session
            await self._cleanup_session(profile_id)

            return {
                "status": "terminated",
                "message": "Browser session terminated successfully"
            }

        except Exception as e:
            logger.error(f"Error terminating session for profile {profile_id}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to terminate session: {str(e)}"
            }
    
    def get_session_status(self, profile_id: str) -> Dict[str, Any]:
        """Get status of browser session."""
        if profile_id not in self.active_sessions:
            return {
                "status": "no_session",
                "message": "No active session found"
            }
        
        session_info = self.active_sessions[profile_id]
        
        if self._is_session_active(session_info):
            elapsed = time.time() - session_info.get("started_at", 0)
            return {
                "status": "active",
                "session_id": session_info["session_id"],
                "elapsed_time": elapsed,
                "timeout_remaining": max(0, self.session_timeout - elapsed)
            }
        else:
            return {
                "status": "expired",
                "message": "Session has expired or browser was closed"
            }
    
    def _get_login_instructions(self) -> List[str]:
        """Get step-by-step login instructions."""
        return [
            "1. Browser window will open with your profile configuration",
            "2. Navigate to Facebook login page (already done)",
            "3. Enter your Facebook credentials manually",
            "4. Complete any 2FA or security checks",
            "5. Once logged in, click 'Complete Login' button in the app"
        ]

    def get_all_active_sessions(self) -> Dict[str, Any]:
        """Get information about all active browser sessions."""
        active_sessions = {}

        for profile_id, session_info in self.active_sessions.items():
            if self._is_session_active(session_info):
                elapsed = time.time() - session_info.get("started_at", 0)
                active_sessions[profile_id] = {
                    "session_id": session_info["session_id"],
                    "status": "active",
                    "elapsed_time": elapsed,
                    "timeout_remaining": max(0, self.session_timeout - elapsed)
                }

        return {
            "total_active_sessions": len(active_sessions),
            "sessions": active_sessions
        }

    async def launch_browser_with_cookies(self, profile: Profile) -> Dict[str, Any]:
        """Launch antidetect browser with saved cookies for profile checking."""
        try:
            logger.info(f"Launching browser with cookies for profile {profile.id}")

            # Check if session already exists
            if profile.id in self.active_sessions:
                session_info = self.active_sessions[profile.id]
                if self._is_session_active(session_info):
                    return {
                        "status": "session_exists",
                        "message": "Browser session already active for this profile",
                        "session_id": session_info["session_id"]
                    }
                else:
                    # Clean up expired session
                    await self._cleanup_session(profile.id)

            # Get zendriver configuration
            zendriver_config = self.browser_service.get_zendriver_config(profile)

            # Create session directory
            session_dir = self._get_session_directory(profile)
            os.makedirs(session_dir, exist_ok=True)

            # Create zendriver script for checking profile with cookies
            script_content = self._create_check_profile_script(profile, zendriver_config)
            script_path = os.path.join(session_dir, "check_profile.py")

            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)

            # Launch the script
            session_id = f"check_{profile.id}_{int(time.time())}"

            # Start the browser process
            process = await asyncio.create_subprocess_exec(
                sys.executable, script_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=session_dir
            )

            # Wait a moment for the process to start and check if it's running
            await asyncio.sleep(2)

            # Check if process is still running (not crashed immediately)
            if process.returncode is not None:
                # Process has already terminated, get error info
                stdout, stderr = await process.communicate()
                error_msg = f"Browser process failed to start. STDERR: {stderr.decode()}, STDOUT: {stdout.decode()}"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "message": f"Failed to launch browser: {error_msg}"
                }

            # Store session info
            self.active_sessions[profile.id] = {
                "session_id": session_id,
                "profile_id": profile.id,
                "profile_name": profile.name,
                "process": process,
                "script_path": script_path,
                "session_dir": session_dir,
                "started_at": time.time(),
                "status": "checking",
                "type": "check_profile"
            }

            logger.info(f"Browser launched for profile checking: {profile.name} (ID: {profile.id})")

            return {
                "status": "browser_launched",
                "message": f"Antidetect browser launched for profile checking: {profile.name}",
                "session_id": session_id,
                "profile_id": profile.id,
                "profile_name": profile.name
            }

        except Exception as e:
            logger.error(f"Error launching browser with cookies for profile {profile.id}: {e}")
            return {
                "status": "error",
                "message": f"Failed to launch browser: {str(e)}"
            }

    def _create_check_profile_script(self, profile: Profile, zendriver_config: Dict[str, Any]) -> str:
        """Create Python script for checking profile with saved cookies."""
        import json
        config_str = json.dumps(zendriver_config, indent=4)
        return f'''#!/usr/bin/env python3
"""
Antidetect browser script for checking profile with saved Facebook cookies.
Profile: {profile.name} (ID: {profile.id})
"""
import asyncio
import json
import logging
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    # Import zendriver from local installation
    zendriver_path = Path(__file__).parent.parent.parent / "zendriver_local"
    sys.path.insert(0, str(zendriver_path))
    import zendriver as zd
    print("✓ Zendriver imported successfully")
except ImportError as e:
    print(f"Import error: {{e}}")
    print("Make sure zendriver is properly installed")
    sys.exit(1)

async def main():
    """Launch antidetect browser for profile checking."""
    try:
        print("Starting antidetect browser for profile checking...")
        print("Profile: {profile.name} (ID: {profile.id})")

        # Browser configuration with antidetect settings
        config_json = """{config_str}"""
        config = json.loads(config_json)
        print(f"Browser config: {{config}}")

        # Ensure browser is not headless for visibility
        config['headless'] = False

        # Add browser args to ensure window is visible
        if 'browser_args' not in config:
            config['browser_args'] = []

        # Remove any headless args and add visibility args
        config['browser_args'] = [arg for arg in config['browser_args'] if '--headless' not in arg]
        config['browser_args'].extend([
            '--new-window',
            '--start-maximized',
            '--disable-background-mode'
        ])

        print(f"Final browser config: {{config}}")

        # Launch browser with zendriver directly using the passed config
        browser = await zd.start(**config)

        if browser:
            print("✅ Browser launched successfully with saved cookies!")
            print("🔍 Checking Facebook login status...")

            # Navigate to Facebook to check login status
            page = await browser.get("https://www.facebook.com/")

            # Wait a bit for page to load
            await asyncio.sleep(3)

            # Check if logged in by looking for profile elements
            try:
                # This is a simple check - in production you'd want more robust detection
                current_url = await page.evaluate("window.location.href")
                page_title = await page.evaluate("document.title")

                print(f"Current URL: {{current_url}}")
                print(f"Page title: {{page_title}}")

                if "login" in current_url.lower() or "login" in page_title.lower():
                    print("⚠️  Not logged in - redirected to login page")
                else:
                    print("✅ Successfully logged in to Facebook!")
                    print("Facebook profile is accessible with saved cookies")

            except Exception as e:
                print(f"Error checking login status: {{e}}")

            print("\\n🌐 Browser is ready for manual inspection")
            print("Close the browser window when done")

            # Keep the browser open until manually closed
            try:
                while True:
                    await asyncio.sleep(1)
                    # Check if browser is still alive
                    try:
                        await page.evaluate("1")
                    except:
                        print("Browser closed by user")
                        break
            except KeyboardInterrupt:
                print("\\nShutting down...")
            finally:
                try:
                    await browser.stop()
                except:
                    pass
        else:
            print("❌ Failed to launch browser")

    except Exception as e:
        print(f"❌ Error: {{e}}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
'''


# Global instance
zendriver_service = ZendriverService()
