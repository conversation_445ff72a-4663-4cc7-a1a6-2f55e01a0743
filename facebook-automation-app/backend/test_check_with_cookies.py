#!/usr/bin/env python3
"""
Test script for checking profile with mock cookies
"""

import asyncio
import aiohttp
import json
import time
import os
from pathlib import Path

async def test_check_profile_with_cookies():
    """Test the check profile API endpoint with mock cookies"""
    print("🧪 Testing check profile API endpoint with mock cookies...")
    
    try:
        # First, create a test profile
        profile_data = {
            "name": f"Test Profile with Cookies {int(time.time())}",
            "browser_config": {
                "browser_type": "chrome",
                "screen_resolution": "1920x1080",
                "timezone": "UTC",
                "language": "en-US"
            },
            "proxy_config": None
        }
        
        async with aiohttp.ClientSession() as session:
            # Create profile
            async with session.post('http://localhost:8000/api/profiles', json=profile_data) as resp:
                if resp.status in [200, 201]:
                    profile = await resp.json()
                    profile_id = profile['id']
                    print(f"✅ Created test profile: {profile['name']} (ID: {profile_id})")
                else:
                    error_text = await resp.text()
                    print(f"❌ Failed to create profile: {resp.status} - {error_text}")
                    return False
            
            # Update profile with Facebook login info
            update_data = {
                "facebook_email": "<EMAIL>",
                "facebook_user_id": "123456789"
            }
            
            async with session.put(f'http://localhost:8000/api/profiles/{profile_id}', json=update_data) as resp:
                if resp.status == 200:
                    print("✅ Updated profile with Facebook login info")
                else:
                    print(f"❌ Failed to update profile: {resp.status}")
            
            # Create mock cookies directory
            profile_dir = Path(f"browser_profiles/profile_{profile_id}")
            default_dir = profile_dir / "Default"
            default_dir.mkdir(parents=True, exist_ok=True)
            
            # Create a mock cookies file
            cookies_file = default_dir / "Cookies"
            cookies_file.write_text("mock cookies data")
            print(f"✅ Created mock cookies file: {cookies_file}")
            
            # Test the check profile endpoint
            async with session.post(f'http://localhost:8000/api/profiles/{profile_id}/check') as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"✅ Check profile API response: {result}")
                    
                    if result.get('status') == 'browser_launched':
                        print("✅ Check profile with cookies test passed!")
                        print(f"   Status: {result.get('status')}")
                        print(f"   Message: {result.get('message')}")
                        return True
                    else:
                        print(f"❌ Unexpected status: {result.get('status')}")
                        print(f"   Message: {result.get('message')}")
                        return False
                else:
                    error_text = await resp.text()
                    print(f"❌ API call failed: {resp.status} - {error_text}")
                    return False
            
            # Clean up - delete test profile and cookies
            async with session.delete(f'http://localhost:8000/api/profiles/{profile_id}') as resp:
                if resp.status == 200:
                    print("✅ Cleaned up test profile")
                else:
                    print(f"⚠️ Failed to clean up profile: {resp.status}")
            
            # Clean up cookies directory
            import shutil
            if profile_dir.exists():
                shutil.rmtree(profile_dir)
                print("✅ Cleaned up cookies directory")
                    
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_check_profile_with_cookies())
    print(f"\n{'✅ Test PASSED' if success else '❌ Test FAILED'}")
