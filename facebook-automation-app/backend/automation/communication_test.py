"""
CommunicationTest - Comprehensive testing for inter-service communication
"""
import asyncio
import time
import json
from typing import Dict, Any, List
from loguru import logger

from .communication_coordinator import CommunicationManager
from .service_communication import ServiceEndpoint, MessageEnvelope
from .interfaces import ServiceEvent, EventType


class CommunicationTester:
    """Comprehensive tester for inter-service communication"""
    
    def __init__(self):
        self.communication_manager = CommunicationManager()
        self.test_results = []
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all communication tests"""
        logger.info("Starting comprehensive communication tests...")
        
        try:
            # Initialize communication manager
            if not await self.communication_manager.initialize():
                return {"error": "Failed to initialize communication manager"}
            
            # Run test suite
            tests = [
                ("Service Registration", self._test_service_registration),
                ("HTTP Communication", self._test_http_communication),
                ("WebSocket Communication", self._test_websocket_communication),
                ("Message Queue", self._test_message_queue),
                ("Event Publishing", self._test_event_publishing),
                ("Health Monitoring", self._test_health_monitoring),
                ("Error Handling", self._test_error_handling),
                ("Performance", self._test_performance)
            ]
            
            for test_name, test_func in tests:
                logger.info(f"Running test: {test_name}")
                try:
                    result = await test_func()
                    self.test_results.append({
                        "test": test_name,
                        "status": "PASSED" if result.get("success", False) else "FAILED",
                        "result": result,
                        "timestamp": time.time()
                    })
                    logger.info(f"Test {test_name}: {'PASSED' if result.get('success', False) else 'FAILED'}")
                except Exception as e:
                    logger.error(f"Test {test_name} failed with exception: {e}")
                    self.test_results.append({
                        "test": test_name,
                        "status": "ERROR",
                        "error": str(e),
                        "timestamp": time.time()
                    })
            
            # Generate summary
            summary = self._generate_test_summary()
            
            return {
                "summary": summary,
                "detailed_results": self.test_results,
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Communication tests failed: {e}")
            return {"error": str(e)}
        
        finally:
            await self.communication_manager.cleanup()
    
    async def _test_service_registration(self) -> Dict[str, Any]:
        """Test service registration functionality"""
        try:
            coordinator = self.communication_manager.coordinator
            
            # Test registering a new service
            test_service_registered = await coordinator.register_service(
                service_name="test_service",
                host="localhost",
                port=9999,
                protocol="http"
            )
            
            if not test_service_registered:
                return {"success": False, "error": "Failed to register test service"}
            
            # Test getting registered service
            service_endpoint = await coordinator.service_registry.get_service("test_service")
            if not service_endpoint:
                return {"success": False, "error": "Failed to retrieve registered service"}
            
            # Test listing services
            services = await coordinator.service_registry.list_services()
            service_names = [s.service_name for s in services]
            
            if "test_service" not in service_names:
                return {"success": False, "error": "Test service not in service list"}
            
            return {
                "success": True,
                "registered_services": len(services),
                "test_service_found": True
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_http_communication(self) -> Dict[str, Any]:
        """Test HTTP communication"""
        try:
            # Test communication with chromedp service (if available)
            result = await self.communication_manager.send_to_chromedp(
                endpoint="/health",
                data={}
            )
            
            chromedp_available = result.get("success", False)
            
            # Test communication with colly service (if available)
            result = await self.communication_manager.send_to_colly(
                endpoint="/health", 
                data={}
            )
            
            colly_available = result.get("success", False)
            
            # Test generic HTTP request
            coordinator = self.communication_manager.coordinator
            generic_result = await coordinator.send_http_request(
                target_service="nonexistent_service",
                endpoint="/test"
            )
            
            # Should fail gracefully
            handles_missing_service = not generic_result.get("success", True)
            
            return {
                "success": True,
                "chromedp_available": chromedp_available,
                "colly_available": colly_available,
                "handles_missing_service": handles_missing_service,
                "http_requests_tested": 3
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_websocket_communication(self) -> Dict[str, Any]:
        """Test WebSocket communication"""
        try:
            coordinator = self.communication_manager.coordinator
            
            # Test WebSocket connection attempt
            ws_result = await coordinator.websocket_communicator.connect_to_service("test_service")
            
            # Test sending message (should handle connection failure gracefully)
            message_result = await coordinator.send_websocket_message(
                target_service="test_service",
                message={"test": "message"}
            )
            
            return {
                "success": True,
                "connection_attempted": True,
                "message_sent": message_result,
                "graceful_failure": True  # Should handle failures gracefully
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_message_queue(self) -> Dict[str, Any]:
        """Test message queue functionality"""
        try:
            coordinator = self.communication_manager.coordinator
            message_queue = coordinator.message_queue
            
            # Test creating queue
            queue_created = await message_queue.create_queue("test_queue")
            if not queue_created:
                return {"success": False, "error": "Failed to create test queue"}
            
            # Test sending message
            test_message = MessageEnvelope(
                message_id="test_msg_001",
                source_service="test_sender",
                target_service="test_receiver",
                message_type="test_message",
                payload={"test_data": "hello world"},
                timestamp=time.time()
            )
            
            message_sent = await message_queue.send_message("test_queue", test_message)
            if not message_sent:
                return {"success": False, "error": "Failed to send test message"}
            
            # Test receiving message
            received_message = await message_queue.receive_message("test_queue", timeout=5.0)
            if not received_message:
                return {"success": False, "error": "Failed to receive test message"}
            
            # Verify message content
            if received_message.message_id != "test_msg_001":
                return {"success": False, "error": "Received message ID mismatch"}
            
            return {
                "success": True,
                "queue_created": True,
                "message_sent": True,
                "message_received": True,
                "message_content_correct": True
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_event_publishing(self) -> Dict[str, Any]:
        """Test event publishing and subscription"""
        try:
            coordinator = self.communication_manager.coordinator
            
            # Create test event
            test_event = ServiceEvent(
                event_type=EventType.PROGRESS_UPDATE,
                source_service="test_service",
                target_service="test_target",
                data={"progress": 50, "message": "Test progress"},
                timestamp=time.time()
            )
            
            # Test event publishing
            event_published = await coordinator.publish_event(test_event)
            if not event_published:
                return {"success": False, "error": "Failed to publish test event"}
            
            # Test event subscription
            events_received = []
            
            async def test_event_handler(event):
                events_received.append(event)
            
            subscription_success = await coordinator.subscribe_to_events(
                [EventType.PROGRESS_UPDATE],
                test_event_handler
            )
            
            if not subscription_success:
                return {"success": False, "error": "Failed to subscribe to events"}
            
            # Publish another event to test subscription
            test_event_2 = ServiceEvent(
                event_type=EventType.PROGRESS_UPDATE,
                source_service="test_service_2",
                target_service="test_target_2",
                data={"progress": 75, "message": "Test progress 2"},
                timestamp=time.time()
            )
            
            await coordinator.publish_event(test_event_2)
            
            # Wait a bit for event processing
            await asyncio.sleep(0.5)
            
            return {
                "success": True,
                "event_published": True,
                "subscription_created": True,
                "events_received": len(events_received),
                "event_handling_works": len(events_received) > 0
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_health_monitoring(self) -> Dict[str, Any]:
        """Test health monitoring functionality"""
        try:
            # Test getting system health
            system_health = await self.communication_manager.get_system_health()
            
            if "error" in system_health:
                return {"success": False, "error": system_health["error"]}
            
            # Test individual service health check
            coordinator = self.communication_manager.coordinator
            chromedp_health = await coordinator.get_service_health("chromedp_extractor")
            colly_health = await coordinator.get_service_health("colly_parser")
            
            # Test connectivity testing
            chromedp_connectivity = await coordinator.test_service_connectivity("chromedp_extractor")
            
            return {
                "success": True,
                "system_health_retrieved": True,
                "services_checked": len(system_health),
                "chromedp_health_status": chromedp_health.get("status", "unknown"),
                "colly_health_status": colly_health.get("status", "unknown"),
                "connectivity_test_completed": "tests" in chromedp_connectivity
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_error_handling(self) -> Dict[str, Any]:
        """Test error handling and recovery"""
        try:
            coordinator = self.communication_manager.coordinator
            
            # Test handling of non-existent service
            result = await coordinator.send_http_request(
                target_service="nonexistent_service",
                endpoint="/test"
            )
            
            handles_missing_service = not result.get("success", True)
            
            # Test handling of invalid endpoint
            result = await coordinator.send_http_request(
                target_service="chromedp_extractor",
                endpoint="/invalid_endpoint"
            )
            
            handles_invalid_endpoint = not result.get("success", True)
            
            # Test message queue overflow handling
            message_queue = coordinator.message_queue
            
            # Try to send message to non-existent queue
            test_message = MessageEnvelope(
                message_id="test_error",
                source_service="test",
                target_service="test",
                message_type="test",
                payload={},
                timestamp=time.time()
            )
            
            # Should handle gracefully
            queue_error_handled = await message_queue.send_message("nonexistent_queue", test_message)
            
            return {
                "success": True,
                "handles_missing_service": handles_missing_service,
                "handles_invalid_endpoint": handles_invalid_endpoint,
                "queue_error_handled": queue_error_handled,
                "error_handling_robust": True
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_performance(self) -> Dict[str, Any]:
        """Test communication performance"""
        try:
            coordinator = self.communication_manager.coordinator
            
            # Test HTTP request performance
            start_time = time.time()
            
            for i in range(5):
                await coordinator.send_http_request(
                    target_service="chromedp_extractor",
                    endpoint="/health",
                    method="GET"
                )
            
            http_time = time.time() - start_time
            avg_http_time = http_time / 5
            
            # Test message queue performance
            message_queue = coordinator.message_queue
            await message_queue.create_queue("perf_test_queue")
            
            start_time = time.time()
            
            for i in range(10):
                test_message = MessageEnvelope(
                    message_id=f"perf_test_{i}",
                    source_service="perf_test",
                    target_service="perf_test",
                    message_type="performance_test",
                    payload={"iteration": i},
                    timestamp=time.time()
                )
                await message_queue.send_message("perf_test_queue", test_message)
            
            queue_time = time.time() - start_time
            avg_queue_time = queue_time / 10
            
            # Get communication stats
            comm_stats = await coordinator.get_communication_stats()
            
            return {
                "success": True,
                "avg_http_request_time": avg_http_time,
                "avg_queue_message_time": avg_queue_time,
                "total_http_requests": comm_stats.get("communication_stats", {}).get("http_requests", 0),
                "total_queue_messages": comm_stats.get("communication_stats", {}).get("queue_messages", 0),
                "performance_acceptable": avg_http_time < 1.0 and avg_queue_time < 0.1
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _generate_test_summary(self) -> Dict[str, Any]:
        """Generate test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r["status"] == "PASSED")
        failed_tests = sum(1 for r in self.test_results if r["status"] == "FAILED")
        error_tests = sum(1 for r in self.test_results if r["status"] == "ERROR")
        
        return {
            "total_tests": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "errors": error_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0.0,
            "overall_status": "PASSED" if failed_tests == 0 and error_tests == 0 else "FAILED"
        }


class CommunicationReporter:
    """Generate detailed communication test reports"""
    
    @staticmethod
    def print_test_report(results: Dict[str, Any]):
        """Print formatted test report"""
        print("\n" + "="*80)
        print("INTER-SERVICE COMMUNICATION TEST REPORT")
        print("="*80)
        
        # Summary
        summary = results.get("summary", {})
        print(f"\n📊 TEST SUMMARY")
        print("-" * 40)
        print(f"Total Tests: {summary.get('total_tests', 0)}")
        print(f"✅ Passed: {summary.get('passed', 0)}")
        print(f"❌ Failed: {summary.get('failed', 0)}")
        print(f"🚨 Errors: {summary.get('errors', 0)}")
        print(f"📈 Success Rate: {summary.get('success_rate', 0):.1%}")
        print(f"🏆 Overall Status: {summary.get('overall_status', 'UNKNOWN')}")
        
        # Detailed results
        print(f"\n📋 DETAILED RESULTS")
        print("-" * 40)
        
        detailed_results = results.get("detailed_results", [])
        for result in detailed_results:
            status_icon = "✅" if result["status"] == "PASSED" else "❌" if result["status"] == "FAILED" else "🚨"
            print(f"\n{status_icon} {result['test']}: {result['status']}")
            
            if result["status"] == "PASSED" and "result" in result:
                test_result = result["result"]
                if isinstance(test_result, dict):
                    for key, value in test_result.items():
                        if key != "success":
                            print(f"    {key}: {value}")
            
            elif result["status"] in ["FAILED", "ERROR"]:
                error_msg = result.get("error", result.get("result", {}).get("error", "Unknown error"))
                print(f"    Error: {error_msg}")
        
        print("\n" + "="*80)


async def main():
    """Main communication test function"""
    try:
        logger.info("Starting Inter-Service Communication Tests")
        
        tester = CommunicationTester()
        results = await tester.run_all_tests()
        
        # Print detailed report
        CommunicationReporter.print_test_report(results)
        
        # Save results to file
        with open("communication_test_results.json", "w") as f:
            json.dump(results, f, indent=2)
        
        logger.info("Communication tests completed. Results saved to communication_test_results.json")
        
    except Exception as e:
        logger.error(f"Communication tests failed: {e}")


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sink=lambda msg: print(msg, end=""),
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # Run communication tests
    asyncio.run(main())
