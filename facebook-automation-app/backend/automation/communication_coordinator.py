"""
CommunicationCoordinator - Central coordinator for all inter-service communication
Manages HTTP, WebSocket, and message queue communication
"""
import asyncio
import time
from typing import Dict, Any, Optional, List, Callable
from dataclasses import asdict
from loguru import logger

from .service_communication import (
    ServiceRegistry, HTTPCommunicator, WebSocketCommunicator, 
    MessageQueue, ServiceEndpoint, MessageEnvelope
)
from .interfaces import ServiceEvent, EventType


class CommunicationCoordinator:
    """Central coordinator for inter-service communication"""
    
    def __init__(self):
        # Core communication components
        self.service_registry = ServiceRegistry()
        self.http_communicator = HTTPCommunicator(self.service_registry)
        self.websocket_communicator = WebSocketCommunicator(self.service_registry)
        self.message_queue = MessageQueue()
        
        # Service monitoring
        self.health_check_interval = 30.0  # seconds
        self.health_check_task: Optional[asyncio.Task] = None
        
        # Communication statistics
        self.stats = {
            "http_requests": 0,
            "websocket_messages": 0,
            "queue_messages": 0,
            "failed_communications": 0,
            "service_health_checks": 0
        }
        
        # Event handlers
        self.event_handlers: Dict[str, List[Callable]] = {}
        
        # Service configurations
        self.default_services = {
            "chromedp_extractor": ServiceEndpoint(
                service_name="chromedp_extractor",
                host="localhost",
                port=8081,
                protocol="http"
            ),
            "colly_parser": ServiceEndpoint(
                service_name="colly_parser", 
                host="localhost",
                port=8082,
                protocol="http"
            )
        }
    
    async def initialize(self) -> bool:
        """Initialize communication coordinator"""
        try:
            logger.info("Initializing CommunicationCoordinator...")
            
            # Initialize HTTP communicator
            if not await self.http_communicator.initialize():
                raise Exception("Failed to initialize HTTP communicator")
            
            # Register default services
            for service_endpoint in self.default_services.values():
                await self.service_registry.register_service(service_endpoint)
            
            # Create default message queues
            await self.message_queue.create_queue("hybrid_events")
            await self.message_queue.create_queue("service_notifications")
            await self.message_queue.create_queue("error_reports")
            
            # Start health monitoring
            self.health_check_task = asyncio.create_task(self._health_monitoring_loop())
            
            logger.info("CommunicationCoordinator initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize CommunicationCoordinator: {e}")
            return False
    
    async def register_service(
        self,
        service_name: str,
        host: str,
        port: int,
        protocol: str = "http"
    ) -> bool:
        """Register a new service"""
        try:
            endpoint = ServiceEndpoint(
                service_name=service_name,
                host=host,
                port=port,
                protocol=protocol
            )
            
            return await self.service_registry.register_service(endpoint)
            
        except Exception as e:
            logger.error(f"Failed to register service {service_name}: {e}")
            return False
    
    async def send_http_request(
        self,
        target_service: str,
        endpoint: str,
        method: str = "POST",
        data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Send HTTP request to service"""
        try:
            self.stats["http_requests"] += 1
            
            result = await self.http_communicator.send_request(
                target_service=target_service,
                endpoint=endpoint,
                method=method,
                data=data
            )
            
            if not result.get("success"):
                self.stats["failed_communications"] += 1
                await self._handle_communication_failure(target_service, "http", result.get("error"))
            
            return result
            
        except Exception as e:
            self.stats["failed_communications"] += 1
            logger.error(f"HTTP request to {target_service} failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def send_websocket_message(
        self,
        target_service: str,
        message: Dict[str, Any]
    ) -> bool:
        """Send WebSocket message to service"""
        try:
            self.stats["websocket_messages"] += 1
            
            # Ensure connection exists
            if target_service not in self.websocket_communicator.connections:
                if not await self.websocket_communicator.connect_to_service(target_service):
                    self.stats["failed_communications"] += 1
                    return False
            
            result = await self.websocket_communicator.send_message(target_service, message)
            
            if not result:
                self.stats["failed_communications"] += 1
                await self._handle_communication_failure(target_service, "websocket", "Send failed")
            
            return result
            
        except Exception as e:
            self.stats["failed_communications"] += 1
            logger.error(f"WebSocket message to {target_service} failed: {e}")
            return False
    
    async def publish_event(self, event: ServiceEvent) -> bool:
        """Publish event to message queue"""
        try:
            self.stats["queue_messages"] += 1
            
            # Create message envelope
            message = MessageEnvelope(
                message_id=f"event_{int(time.time() * 1000)}",
                source_service=event.source_service,
                target_service=event.target_service or "broadcast",
                message_type="service_event",
                payload=asdict(event),
                timestamp=time.time(),
                ttl=300.0  # 5 minutes TTL
            )
            
            # Send to hybrid events queue
            result = await self.message_queue.send_message("hybrid_events", message)
            
            if not result:
                self.stats["failed_communications"] += 1
            
            # Notify event handlers
            await self._notify_event_handlers(event)
            
            return result
            
        except Exception as e:
            self.stats["failed_communications"] += 1
            logger.error(f"Failed to publish event: {e}")
            return False
    
    async def subscribe_to_events(
        self,
        event_types: List[EventType],
        callback: Callable
    ) -> bool:
        """Subscribe to specific event types"""
        try:
            for event_type in event_types:
                event_key = event_type.value
                if event_key not in self.event_handlers:
                    self.event_handlers[event_key] = []
                
                if callback not in self.event_handlers[event_key]:
                    self.event_handlers[event_key].append(callback)
            
            logger.info(f"Subscribed to events: {[et.value for et in event_types]}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to subscribe to events: {e}")
            return False
    
    async def _notify_event_handlers(self, event: ServiceEvent):
        """Notify event handlers"""
        try:
            event_key = event.event_type.value
            handlers = self.event_handlers.get(event_key, [])
            
            for handler in handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(event)
                    else:
                        handler(event)
                except Exception as e:
                    logger.error(f"Error in event handler: {e}")
                    
        except Exception as e:
            logger.error(f"Error notifying event handlers: {e}")
    
    async def _handle_communication_failure(
        self,
        service_name: str,
        communication_type: str,
        error: str
    ):
        """Handle communication failure"""
        try:
            # Create error event
            error_event = ServiceEvent(
                event_type=EventType.ERROR_OCCURRED,
                source_service="communication_coordinator",
                target_service=service_name,
                data={
                    "communication_type": communication_type,
                    "error": error,
                    "timestamp": time.time()
                },
                timestamp=time.time()
            )
            
            # Send to error queue
            error_message = MessageEnvelope(
                message_id=f"error_{int(time.time() * 1000)}",
                source_service="communication_coordinator",
                target_service="error_handler",
                message_type="communication_error",
                payload=asdict(error_event),
                timestamp=time.time()
            )
            
            await self.message_queue.send_message("error_reports", error_message)
            
            logger.warning(f"Communication failure with {service_name} ({communication_type}): {error}")
            
        except Exception as e:
            logger.error(f"Error handling communication failure: {e}")
    
    async def _health_monitoring_loop(self):
        """Background health monitoring loop"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                # Check health of all services
                health_results = await self.service_registry.check_all_services_health()
                self.stats["service_health_checks"] += len(health_results)
                
                # Process health results
                for service_name, health_data in health_results.items():
                    if health_data.get("status") != "healthy":
                        # Create health alert event
                        health_event = ServiceEvent(
                            event_type=EventType.ERROR_OCCURRED,
                            source_service="communication_coordinator",
                            target_service=service_name,
                            data={
                                "health_status": health_data,
                                "alert_type": "service_unhealthy"
                            },
                            timestamp=time.time()
                        )
                        
                        await self.publish_event(health_event)
                
                logger.debug(f"Health check completed for {len(health_results)} services")
                
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {e}")
    
    async def get_service_health(self, service_name: str) -> Dict[str, Any]:
        """Get health status of specific service"""
        return await self.service_registry.check_service_health(service_name)
    
    async def get_all_services_health(self) -> Dict[str, Dict[str, Any]]:
        """Get health status of all services"""
        return await self.service_registry.check_all_services_health()
    
    async def get_communication_stats(self) -> Dict[str, Any]:
        """Get communication statistics"""
        try:
            # Get queue stats
            queue_stats = {}
            for queue_name in ["hybrid_events", "service_notifications", "error_reports"]:
                queue_stats[queue_name] = await self.message_queue.get_queue_stats(queue_name)
            
            return {
                "communication_stats": self.stats,
                "queue_stats": queue_stats,
                "registered_services": len(self.service_registry.services),
                "active_websocket_connections": len(self.websocket_communicator.connections),
                "event_handlers": {
                    event_type: len(handlers) 
                    for event_type, handlers in self.event_handlers.items()
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting communication stats: {e}")
            return {"error": str(e)}
    
    async def test_service_connectivity(self, service_name: str) -> Dict[str, Any]:
        """Test connectivity to specific service"""
        try:
            results = {
                "service_name": service_name,
                "timestamp": time.time(),
                "tests": {}
            }
            
            # Test HTTP connectivity
            http_result = await self.send_http_request(
                target_service=service_name,
                endpoint="/health",
                method="GET"
            )
            results["tests"]["http"] = {
                "success": http_result.get("success", False),
                "status": http_result.get("status"),
                "error": http_result.get("error")
            }
            
            # Test WebSocket connectivity (if supported)
            try:
                ws_result = await self.websocket_communicator.connect_to_service(service_name)
                results["tests"]["websocket"] = {
                    "success": ws_result,
                    "error": None if ws_result else "Connection failed"
                }
            except Exception as e:
                results["tests"]["websocket"] = {
                    "success": False,
                    "error": str(e)
                }
            
            return results
            
        except Exception as e:
            logger.error(f"Error testing connectivity to {service_name}: {e}")
            return {
                "service_name": service_name,
                "error": str(e),
                "tests": {}
            }
    
    async def broadcast_message(
        self,
        message: Dict[str, Any],
        exclude_services: Optional[List[str]] = None
    ) -> Dict[str, bool]:
        """Broadcast message to all registered services"""
        try:
            exclude_services = exclude_services or []
            results = {}
            
            services = await self.service_registry.list_services()
            
            for service_endpoint in services:
                if service_endpoint.service_name in exclude_services:
                    continue
                
                # Try HTTP first
                http_result = await self.send_http_request(
                    target_service=service_endpoint.service_name,
                    endpoint="/broadcast",
                    method="POST",
                    data=message
                )
                
                results[service_endpoint.service_name] = http_result.get("success", False)
            
            return results
            
        except Exception as e:
            logger.error(f"Error broadcasting message: {e}")
            return {}
    
    async def cleanup(self):
        """Cleanup communication coordinator"""
        try:
            logger.info("Cleaning up CommunicationCoordinator...")
            
            # Cancel health monitoring
            if self.health_check_task:
                self.health_check_task.cancel()
                try:
                    await self.health_check_task
                except asyncio.CancelledError:
                    pass
            
            # Cleanup components
            await self.http_communicator.cleanup()
            await self.websocket_communicator.cleanup()
            await self.message_queue.cleanup()
            
            # Clear event handlers
            self.event_handlers.clear()
            
            logger.info("CommunicationCoordinator cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during CommunicationCoordinator cleanup: {e}")


class CommunicationManager:
    """High-level manager for communication coordination"""
    
    def __init__(self):
        self.coordinator = CommunicationCoordinator()
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize communication manager"""
        try:
            if await self.coordinator.initialize():
                self.initialized = True
                logger.info("CommunicationManager initialized successfully")
                return True
            else:
                logger.error("Failed to initialize CommunicationManager")
                return False
                
        except Exception as e:
            logger.error(f"Error initializing CommunicationManager: {e}")
            return False
    
    async def send_to_chromedp(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Send request to ChromedpExtractor service"""
        return await self.coordinator.send_http_request(
            target_service="chromedp_extractor",
            endpoint=endpoint,
            data=data
        )
    
    async def send_to_colly(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Send request to CollyParser service"""
        return await self.coordinator.send_http_request(
            target_service="colly_parser",
            endpoint=endpoint,
            data=data
        )
    
    async def publish_hybrid_event(self, event: ServiceEvent) -> bool:
        """Publish event to hybrid system"""
        return await self.coordinator.publish_event(event)
    
    async def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health"""
        if not self.initialized:
            return {"error": "CommunicationManager not initialized"}
        
        return await self.coordinator.get_all_services_health()
    
    async def cleanup(self):
        """Cleanup communication manager"""
        try:
            if self.coordinator:
                await self.coordinator.cleanup()
            self.initialized = False
            logger.info("CommunicationManager cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up CommunicationManager: {e}")
