"""
IntegrationTesting - Comprehensive integration testing framework for hybrid system
"""
import asyncio
import time
import json
import random
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from loguru import logger

from .hybrid_integration import HybridServiceFactory
from .communication_coordinator import CommunicationManager
from .service_discovery import ServiceDiscovery
from .interfaces import ServiceEvent, EventType


@dataclass
class IntegrationTestCase:
    """Integration test case definition"""
    test_id: str
    name: str
    description: str
    test_type: str  # component, workflow, error_handling, recovery
    dependencies: List[str]
    expected_outcome: str
    timeout_seconds: float = 60.0
    retry_attempts: int = 3


class IntegrationTestRunner:
    """Comprehensive integration test runner"""
    
    def __init__(self):
        self.test_results: List[Dict[str, Any]] = []
        self.test_cases = self._define_test_cases()
        self.services_under_test = {}
        
    def _define_test_cases(self) -> List[IntegrationTestCase]:
        """Define comprehensive integration test cases"""
        return [
            # Component Integration Tests
            IntegrationTestCase(
                test_id="INT001",
                name="Service Discovery Integration",
                description="Test automatic service discovery and registration",
                test_type="component",
                dependencies=["service_discovery"],
                expected_outcome="Services automatically discovered and registered"
            ),
            
            IntegrationTestCase(
                test_id="INT002", 
                name="Communication Layer Integration",
                description="Test HTTP, WebSocket, and message queue communication",
                test_type="component",
                dependencies=["communication_manager"],
                expected_outcome="All communication channels working properly"
            ),
            
            IntegrationTestCase(
                test_id="INT003",
                name="ChromedpExtractor Integration",
                description="Test integration with ChromedpExtractor Go service",
                test_type="component",
                dependencies=["chromedp_extractor"],
                expected_outcome="HTML extraction working via HTTP API"
            ),
            
            IntegrationTestCase(
                test_id="INT004",
                name="CollyParser Integration", 
                description="Test integration with CollyParser Go service",
                test_type="component",
                dependencies=["colly_parser"],
                expected_outcome="UID parsing working via HTTP API"
            ),
            
            # Workflow Integration Tests
            IntegrationTestCase(
                test_id="INT005",
                name="End-to-End Hybrid Workflow",
                description="Test complete hybrid scraping workflow",
                test_type="workflow",
                dependencies=["hybrid_coordinator", "browser_manager", "chromedp_extractor", "colly_parser"],
                expected_outcome="Complete scraping workflow successful",
                timeout_seconds=120.0
            ),
            
            IntegrationTestCase(
                test_id="INT006",
                name="Concurrent Session Management",
                description="Test multiple concurrent scraping sessions",
                test_type="workflow", 
                dependencies=["hybrid_coordinator"],
                expected_outcome="Multiple sessions handled without conflicts",
                timeout_seconds=180.0
            ),
            
            IntegrationTestCase(
                test_id="INT007",
                name="Event-Driven Coordination",
                description="Test event publishing and subscription across services",
                test_type="workflow",
                dependencies=["event_bus", "communication_manager"],
                expected_outcome="Events properly propagated between services"
            ),
            
            # Error Handling Tests
            IntegrationTestCase(
                test_id="INT008",
                name="Service Failure Recovery",
                description="Test recovery when Go services are unavailable",
                test_type="error_handling",
                dependencies=["hybrid_coordinator"],
                expected_outcome="Graceful fallback to legacy mode"
            ),
            
            IntegrationTestCase(
                test_id="INT009",
                name="Network Failure Handling",
                description="Test handling of network communication failures",
                test_type="error_handling",
                dependencies=["communication_manager"],
                expected_outcome="Retry logic and error recovery working"
            ),
            
            IntegrationTestCase(
                test_id="INT010",
                name="Memory Pressure Recovery",
                description="Test system behavior under memory pressure",
                test_type="error_handling",
                dependencies=["memory_profiler", "hybrid_coordinator"],
                expected_outcome="Emergency cleanup and graceful degradation"
            ),
            
            # Recovery Tests
            IntegrationTestCase(
                test_id="INT011",
                name="Service Auto-Recovery",
                description="Test automatic service recovery after failures",
                test_type="recovery",
                dependencies=["service_discovery", "communication_manager"],
                expected_outcome="Services automatically reconnect after recovery"
            ),
            
            IntegrationTestCase(
                test_id="INT012",
                name="Data Consistency Recovery",
                description="Test data consistency after partial failures",
                test_type="recovery",
                dependencies=["shared_memory", "hybrid_coordinator"],
                expected_outcome="Data integrity maintained during recovery"
            )
        ]
    
    async def run_all_integration_tests(self) -> Dict[str, Any]:
        """Run all integration tests"""
        logger.info("🧪 Starting Comprehensive Integration Testing")
        logger.info("="*80)
        
        start_time = time.time()
        
        # Initialize test environment
        await self._setup_test_environment()
        
        # Run tests by category
        test_categories = ["component", "workflow", "error_handling", "recovery"]
        
        for category in test_categories:
            logger.info(f"\n📋 Running {category.upper()} tests...")
            category_tests = [tc for tc in self.test_cases if tc.test_type == category]
            
            for test_case in category_tests:
                await self._run_single_test(test_case)
        
        # Cleanup test environment
        await self._cleanup_test_environment()
        
        # Generate test report
        test_duration = time.time() - start_time
        report = self._generate_test_report(test_duration)
        
        logger.info(f"\n🎉 Integration testing completed in {test_duration:.1f} seconds")
        
        return report
    
    async def _setup_test_environment(self):
        """Setup test environment"""
        try:
            logger.info("Setting up test environment...")
            
            # Initialize communication manager
            self.services_under_test["communication_manager"] = CommunicationManager()
            await self.services_under_test["communication_manager"].initialize()
            
            # Initialize service discovery
            self.services_under_test["service_discovery"] = ServiceDiscovery()
            await self.services_under_test["service_discovery"].start_discovery()
            
            logger.info("Test environment setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup test environment: {e}")
            raise
    
    async def _cleanup_test_environment(self):
        """Cleanup test environment"""
        try:
            logger.info("Cleaning up test environment...")
            
            # Cleanup services
            for service_name, service in self.services_under_test.items():
                try:
                    if hasattr(service, 'cleanup'):
                        await service.cleanup()
                    elif hasattr(service, 'stop_discovery'):
                        await service.stop_discovery()
                except Exception as e:
                    logger.warning(f"Error cleaning up {service_name}: {e}")
            
            self.services_under_test.clear()
            logger.info("Test environment cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during test environment cleanup: {e}")
    
    async def _run_single_test(self, test_case: IntegrationTestCase):
        """Run single integration test"""
        logger.info(f"Running {test_case.test_id}: {test_case.name}")
        
        test_start_time = time.time()
        test_result = {
            "test_id": test_case.test_id,
            "name": test_case.name,
            "description": test_case.description,
            "test_type": test_case.test_type,
            "start_time": test_start_time,
            "status": "UNKNOWN",
            "duration": 0.0,
            "error_message": None,
            "details": {}
        }
        
        try:
            # Run test with timeout
            test_task = asyncio.create_task(self._execute_test(test_case))
            result = await asyncio.wait_for(test_task, timeout=test_case.timeout_seconds)
            
            test_result.update(result)
            test_result["status"] = "PASSED" if result.get("success", False) else "FAILED"
            
        except asyncio.TimeoutError:
            test_result["status"] = "TIMEOUT"
            test_result["error_message"] = f"Test timed out after {test_case.timeout_seconds} seconds"
            
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error_message"] = str(e)
            logger.error(f"Test {test_case.test_id} failed with error: {e}")
        
        finally:
            test_result["duration"] = time.time() - test_start_time
            self.test_results.append(test_result)
            
            status_icon = "✅" if test_result["status"] == "PASSED" else "❌"
            logger.info(f"{status_icon} {test_case.test_id}: {test_result['status']} ({test_result['duration']:.2f}s)")
    
    async def _execute_test(self, test_case: IntegrationTestCase) -> Dict[str, Any]:
        """Execute specific test case"""
        test_method_name = f"_test_{test_case.test_id.lower()}"
        
        if hasattr(self, test_method_name):
            test_method = getattr(self, test_method_name)
            return await test_method(test_case)
        else:
            return await self._generic_test_execution(test_case)
    
    async def _generic_test_execution(self, test_case: IntegrationTestCase) -> Dict[str, Any]:
        """Generic test execution for undefined test methods"""
        return {
            "success": True,
            "message": f"Generic test execution for {test_case.test_id}",
            "details": {"note": "Test method not specifically implemented"}
        }
    
    # Specific test implementations
    async def _test_int001(self, test_case: IntegrationTestCase) -> Dict[str, Any]:
        """Test service discovery integration"""
        try:
            service_discovery = self.services_under_test.get("service_discovery")
            if not service_discovery:
                return {"success": False, "message": "Service discovery not available"}
            
            # Announce a test service
            announced = await service_discovery.announce_service(
                service_name="test_integration_service",
                host="localhost",
                port=9999,
                capabilities=["testing"]
            )
            
            if not announced:
                return {"success": False, "message": "Failed to announce test service"}
            
            # Discover services
            await asyncio.sleep(2)  # Wait for announcement
            discovered_services = await service_discovery.discover_services(timeout=5.0)
            
            # Check if our test service was discovered
            test_service_found = any(
                s.service_name == "test_integration_service" 
                for s in discovered_services
            )
            
            return {
                "success": test_service_found,
                "message": "Service discovery working" if test_service_found else "Test service not discovered",
                "details": {
                    "announced": announced,
                    "discovered_count": len(discovered_services),
                    "test_service_found": test_service_found
                }
            }
            
        except Exception as e:
            return {"success": False, "message": f"Service discovery test failed: {e}"}
    
    async def _test_int002(self, test_case: IntegrationTestCase) -> Dict[str, Any]:
        """Test communication layer integration"""
        try:
            comm_manager = self.services_under_test.get("communication_manager")
            if not comm_manager:
                return {"success": False, "message": "Communication manager not available"}
            
            # Test HTTP communication
            http_result = await comm_manager.send_to_chromedp("/health", {})
            http_success = http_result.get("success", False)
            
            # Test event publishing
            test_event = ServiceEvent(
                event_type=EventType.PROGRESS_UPDATE,
                source_service="integration_test",
                target_service="test_target",
                data={"test": "integration"},
                timestamp=time.time()
            )
            
            event_success = await comm_manager.publish_hybrid_event(test_event)
            
            # Test system health
            health = await comm_manager.get_system_health()
            health_success = "error" not in health
            
            overall_success = http_success or event_success or health_success  # At least one should work
            
            return {
                "success": overall_success,
                "message": "Communication layer working" if overall_success else "Communication layer failed",
                "details": {
                    "http_communication": http_success,
                    "event_publishing": event_success,
                    "health_check": health_success
                }
            }
            
        except Exception as e:
            return {"success": False, "message": f"Communication test failed: {e}"}
    
    async def _test_int005(self, test_case: IntegrationTestCase) -> Dict[str, Any]:
        """Test end-to-end hybrid workflow"""
        try:
            # Create hybrid service
            service = HybridServiceFactory.create_development_service()
            
            # Initialize service
            if not await service.initialize():
                return {"success": False, "message": "Failed to initialize hybrid service"}
            
            # Run scraping workflow
            result = await service.scrape_facebook_post_uids(
                profile_id="integration_test_profile",
                post_url="https://facebook.com/test/integration"
            )
            
            # Cleanup
            await service.cleanup()
            
            success = result.get("success", False)
            
            return {
                "success": success,
                "message": "End-to-end workflow working" if success else "Workflow failed",
                "details": {
                    "extraction_method": result.get("metadata", {}).get("extraction_method", "unknown"),
                    "processing_time": result.get("processing_time", 0),
                    "total_uids": result.get("total_uids", 0)
                }
            }
            
        except Exception as e:
            return {"success": False, "message": f"End-to-end workflow test failed: {e}"}
    
    async def _test_int006(self, test_case: IntegrationTestCase) -> Dict[str, Any]:
        """Test concurrent session management"""
        try:
            # Create multiple services
            services = []
            for i in range(3):
                service = HybridServiceFactory.create_development_service()
                if await service.initialize():
                    services.append(service)
            
            if not services:
                return {"success": False, "message": "Failed to initialize any services"}
            
            # Run concurrent scraping
            tasks = []
            for i, service in enumerate(services):
                task = asyncio.create_task(
                    service.scrape_facebook_post_uids(
                        profile_id=f"concurrent_test_{i}",
                        post_url=f"https://facebook.com/test/concurrent/{i}"
                    )
                )
                tasks.append(task)
            
            # Wait for all tasks
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Cleanup services
            for service in services:
                await service.cleanup()
            
            # Analyze results
            successful_results = [r for r in results if isinstance(r, dict) and r.get("success", False)]
            success_rate = len(successful_results) / len(results) if results else 0
            
            return {
                "success": success_rate >= 0.5,  # At least 50% success rate
                "message": f"Concurrent sessions: {success_rate:.1%} success rate",
                "details": {
                    "total_sessions": len(results),
                    "successful_sessions": len(successful_results),
                    "success_rate": success_rate
                }
            }
            
        except Exception as e:
            return {"success": False, "message": f"Concurrent session test failed: {e}"}
    
    async def _test_int008(self, test_case: IntegrationTestCase) -> Dict[str, Any]:
        """Test service failure recovery"""
        try:
            # Create service with fallback enabled
            service = HybridServiceFactory.create_service(
                performance_mode="balanced",
                enable_fallback=True
            )
            
            if not await service.initialize():
                return {"success": False, "message": "Failed to initialize service"}
            
            # Simulate service failure by using invalid service endpoints
            # The system should fallback to legacy mode
            result = await service.scrape_facebook_post_uids(
                profile_id="failure_recovery_test",
                post_url="https://facebook.com/test/failure_recovery"
            )
            
            await service.cleanup()
            
            # Check if fallback mode was used
            extraction_method = result.get("metadata", {}).get("extraction_method", "unknown")
            fallback_used = "fallback" in extraction_method or "zendriver" in extraction_method
            
            return {
                "success": result.get("success", False),
                "message": "Fallback recovery working" if fallback_used else "No fallback detected",
                "details": {
                    "extraction_method": extraction_method,
                    "fallback_used": fallback_used,
                    "processing_time": result.get("processing_time", 0)
                }
            }
            
        except Exception as e:
            return {"success": False, "message": f"Service failure recovery test failed: {e}"}
    
    def _generate_test_report(self, test_duration: float) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        try:
            # Calculate statistics
            total_tests = len(self.test_results)
            passed_tests = sum(1 for r in self.test_results if r["status"] == "PASSED")
            failed_tests = sum(1 for r in self.test_results if r["status"] == "FAILED")
            error_tests = sum(1 for r in self.test_results if r["status"] == "ERROR")
            timeout_tests = sum(1 for r in self.test_results if r["status"] == "TIMEOUT")
            
            success_rate = passed_tests / total_tests if total_tests > 0 else 0
            
            # Categorize results
            results_by_category = {}
            for result in self.test_results:
                category = result["test_type"]
                if category not in results_by_category:
                    results_by_category[category] = []
                results_by_category[category].append(result)
            
            # Generate recommendations
            recommendations = self._generate_integration_recommendations()
            
            return {
                "timestamp": time.time(),
                "test_duration": test_duration,
                "summary": {
                    "total_tests": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "errors": error_tests,
                    "timeouts": timeout_tests,
                    "success_rate": success_rate,
                    "overall_status": "PASSED" if success_rate >= 0.8 else "FAILED"
                },
                "results_by_category": results_by_category,
                "detailed_results": self.test_results,
                "recommendations": recommendations,
                "integration_health": self._assess_integration_health()
            }
            
        except Exception as e:
            logger.error(f"Error generating test report: {e}")
            return {"error": str(e)}
    
    def _generate_integration_recommendations(self) -> List[str]:
        """Generate integration recommendations based on test results"""
        recommendations = []
        
        try:
            # Analyze failed tests
            failed_tests = [r for r in self.test_results if r["status"] in ["FAILED", "ERROR"]]
            
            if failed_tests:
                recommendations.append("Address failed integration tests before production deployment")
                
                # Specific recommendations based on failed test types
                failed_categories = set(r["test_type"] for r in failed_tests)
                
                if "component" in failed_categories:
                    recommendations.append("Fix component integration issues - check service connectivity")
                
                if "workflow" in failed_categories:
                    recommendations.append("Optimize workflow integration - review data flow and timing")
                
                if "error_handling" in failed_categories:
                    recommendations.append("Improve error handling mechanisms - enhance recovery strategies")
                
                if "recovery" in failed_categories:
                    recommendations.append("Strengthen recovery mechanisms - implement better failover logic")
            
            # Analyze timeout tests
            timeout_tests = [r for r in self.test_results if r["status"] == "TIMEOUT"]
            if timeout_tests:
                recommendations.append("Investigate timeout issues - consider increasing timeouts or optimizing performance")
            
            # Success rate recommendations
            success_rate = sum(1 for r in self.test_results if r["status"] == "PASSED") / len(self.test_results)
            
            if success_rate < 0.8:
                recommendations.append("Low integration success rate - comprehensive system review required")
            elif success_rate < 0.9:
                recommendations.append("Moderate integration success rate - minor improvements needed")
            else:
                recommendations.append("Excellent integration success rate - system ready for production")
            
            # General recommendations
            recommendations.extend([
                "Implement continuous integration testing",
                "Set up integration test monitoring and alerting",
                "Create integration test regression suite",
                "Document integration test procedures"
            ])
            
        except Exception as e:
            recommendations.append(f"Error generating recommendations: {e}")
        
        return recommendations
    
    def _assess_integration_health(self) -> Dict[str, Any]:
        """Assess overall integration health"""
        try:
            if not self.test_results:
                return {"status": "unknown", "score": 0}
            
            # Calculate health score
            total_tests = len(self.test_results)
            passed_tests = sum(1 for r in self.test_results if r["status"] == "PASSED")
            
            health_score = (passed_tests / total_tests) * 100
            
            # Determine health status
            if health_score >= 95:
                health_status = "excellent"
            elif health_score >= 85:
                health_status = "good"
            elif health_score >= 70:
                health_status = "fair"
            else:
                health_status = "poor"
            
            # Category-specific health
            category_health = {}
            results_by_category = {}
            
            for result in self.test_results:
                category = result["test_type"]
                if category not in results_by_category:
                    results_by_category[category] = []
                results_by_category[category].append(result)
            
            for category, results in results_by_category.items():
                passed = sum(1 for r in results if r["status"] == "PASSED")
                total = len(results)
                category_score = (passed / total) * 100 if total > 0 else 0
                category_health[category] = {
                    "score": category_score,
                    "passed": passed,
                    "total": total
                }
            
            return {
                "status": health_status,
                "score": health_score,
                "category_health": category_health,
                "ready_for_production": health_score >= 85
            }
            
        except Exception as e:
            return {"status": "error", "error": str(e)}


async def main():
    """Main integration testing function"""
    try:
        logger.info("🧪 Starting Integration Testing Suite")
        
        # Create test runner
        test_runner = IntegrationTestRunner()
        
        # Run all integration tests
        results = await test_runner.run_all_integration_tests()
        
        # Save results
        with open("integration_test_results.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        # Print summary
        print("\n" + "="*80)
        print("INTEGRATION TESTING SUMMARY")
        print("="*80)
        
        summary = results.get("summary", {})
        print(f"Total Tests: {summary.get('total_tests', 0)}")
        print(f"✅ Passed: {summary.get('passed', 0)}")
        print(f"❌ Failed: {summary.get('failed', 0)}")
        print(f"🚨 Errors: {summary.get('errors', 0)}")
        print(f"⏰ Timeouts: {summary.get('timeouts', 0)}")
        print(f"📈 Success Rate: {summary.get('success_rate', 0):.1%}")
        print(f"🏆 Overall Status: {summary.get('overall_status', 'UNKNOWN')}")
        
        # Integration health
        health = results.get("integration_health", {})
        print(f"\n🏥 Integration Health: {health.get('status', 'unknown').upper()}")
        print(f"📊 Health Score: {health.get('score', 0):.1f}/100")
        print(f"🚀 Production Ready: {'YES' if health.get('ready_for_production', False) else 'NO'}")
        
        print("\n" + "="*80)
        
        logger.info("Integration testing completed. Results saved to integration_test_results.json")
        
    except Exception as e:
        logger.error(f"Integration testing failed: {e}")


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sink=lambda msg: print(msg, end=""),
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # Run integration testing
    asyncio.run(main())
