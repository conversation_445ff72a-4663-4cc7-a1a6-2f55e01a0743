package main

import (
	"log"
	"net/http"
	"os"
	"regexp"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

type ParseRequest struct {
	HTML    string `json:"html"`
	Options struct {
		ExtractComments bool `json:"extract_comments"`
		ExtractLikes    bool `json:"extract_likes"`
		ExtractShares   bool `json:"extract_shares"`
	} `json:"options"`
}

type ParseResponse struct {
	Success   bool     `json:"success"`
	UIDs      []string `json:"uids"`
	TotalUIDs int      `json:"total_uids"`
	Error     string   `json:"error,omitempty"`
}

func main() {
	// Load environment variables
	godotenv.Load()

	// Set up Gin
	if os.Getenv("DEBUG") != "true" {
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.Default()

	// Health check endpoint
	r.GET("/health", func(c *gin.Context) {
		c.<PERSON><PERSON>(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": "colly-parser",
			"time":    time.Now().Unix(),
		})
	})

	// Parse HTML endpoint
	r.POST("/parse", parseHTML)

	// Get port from environment or use default
	port := os.Getenv("PORT")
	if port == "" {
		port = "8082"
	}

	log.Printf("CollyParser service starting on port %s", port)
	r.Run(":" + port)
}

func parseHTML(c *gin.Context) {
	var req ParseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ParseResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	var uids []string
	uidMap := make(map[string]bool) // For deduplication

	// UID extraction regex
	uidRegex := regexp.MustCompile(`/user/(\d+)`)

	// Simple regex-based parsing for now
	matches := uidRegex.FindAllStringSubmatch(req.HTML, -1)
	for _, match := range matches {
		if len(match) > 1 {
			uid := match[1]
			if !uidMap[uid] {
				uidMap[uid] = true
				uids = append(uids, uid)
			}
		}
	}

	c.JSON(http.StatusOK, ParseResponse{
		Success:   true,
		UIDs:      uids,
		TotalUIDs: len(uids),
	})
}
