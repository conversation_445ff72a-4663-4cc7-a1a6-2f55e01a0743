# ChromedpExtractor Service Dockerfile
# Multi-stage build for Go service

# Stage 1: Build stage
FROM golang:1.21-alpine as builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags="-s -w" -o chromedp-extractor main.go

# Stage 2: Production stage
FROM alpine:latest as production

# Install runtime dependencies
RUN apk --no-cache add \
    ca-certificates \
    chromium \
    curl \
    tzdata \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S appuser && \
    adduser -u 1001 -S appuser -G appuser

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/chromedp-extractor .

# Create necessary directories
RUN mkdir -p /app/logs /app/temp \
    && chown -R appuser:appuser /app

# Create startup script
RUN cat > /app/start.sh << 'EOF'
#!/bin/sh
set -e

echo "Starting ChromedpExtractor Service..."

# Set Chrome path for Alpine
export CHROME_BIN=/usr/bin/chromium-browser
export CHROME_PATH=/usr/bin/chromium-browser

# Start the service
exec ./chromedp-extractor
EOF

RUN chmod +x /app/start.sh

# Health check script
RUN cat > /app/healthcheck.sh << 'EOF'
#!/bin/sh
curl -f http://localhost:${PORT:-8081}/health || exit 1
EOF

RUN chmod +x /app/healthcheck.sh

# Switch to non-root user
USER appuser

# Set environment variables
ENV PORT=8081
ENV HOST=0.0.0.0
ENV MAX_CONNECTIONS=20
ENV TIMEOUT=30s
ENV DEBUG=false
ENV CHROME_BIN=/usr/bin/chromium-browser

# Expose port
EXPOSE 8081

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD /app/healthcheck.sh

# Start command
CMD ["/app/start.sh"]
