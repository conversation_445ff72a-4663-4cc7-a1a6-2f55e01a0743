package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/chromedp/chromedp"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

type ExtractRequest struct {
	URL     string `json:"url"`
	Timeout int    `json:"timeout"`
}

type ExtractResponse struct {
	Success bool   `json:"success"`
	HTML    string `json:"html"`
	Error   string `json:"error,omitempty"`
}

func main() {
	// Load environment variables
	godotenv.Load()

	// Set up Gin
	if os.Getenv("DEBUG") != "true" {
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.Default()

	// Health check endpoint
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": "chromedp-extractor",
			"time":    time.Now().Unix(),
		})
	})

	// Extract HTML endpoint
	r.POST("/extract", extractHTML)

	// Get port from environment or use default
	port := os.Getenv("PORT")
	if port == "" {
		port = "8081"
	}

	log.Printf("ChromedpExtractor service starting on port %s", port)
	r.Run(":" + port)
}

func extractHTML(c *gin.Context) {
	var req ExtractRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ExtractResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	// Set default timeout
	if req.Timeout == 0 {
		req.Timeout = 30
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(req.Timeout)*time.Second)
	defer cancel()

	// Create Chrome context
	chromeCtx, chromeCancel := chromedp.NewContext(ctx)
	defer chromeCancel()

	var html string
	err := chromedp.Run(chromeCtx,
		chromedp.Navigate(req.URL),
		chromedp.WaitVisible("body", chromedp.ByQuery),
		chromedp.OuterHTML("html", &html),
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, ExtractResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ExtractResponse{
		Success: true,
		HTML:    html,
	})
}
